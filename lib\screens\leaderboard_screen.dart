import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/controllers/data_controller.dart';
import '/services/leaderboard_service.dart';
import '/models/user_data.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final LeaderboardService _leaderboardService = LeaderboardService();
  List<UserData> _leaderboardData = [];
  bool _isLoading = true;
  UserData? _currentUser;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _loadLeaderboardData();
    _animationController.forward();
  }

  Future<void> _loadLeaderboardData() async {
    try {
      // Initialize leaderboard with sample data if empty
      await _leaderboardService.initializeLeaderboard();

      // Load leaderboard data
      final leaderboardData = await _leaderboardService.getLeaderboard();

      // Get current user data
      final dataController = Get.find<DataController>();
      final currentUserId = await _leaderboardService.getCurrentUserId();

      // Update current user data in leaderboard
      if (dataController.userName.value.isNotEmpty) {
        final currentUserData = UserData(
          id: currentUserId,
          name: dataController.userName.value,
          totalScore: dataController.totalScore.value,
          gamesPlayed: dataController.gamesPlayed.value,
          level: dataController.getUserLevel(),
          avatar: _leaderboardService.getRandomAvatar(),
          averageScore: dataController.averageScore.value,
          coins: dataController.coins.value,
          dailyStreak: dataController.dailyQuizStreak.value,
          lastPlayedDate: DateTime.now(),
          joinDate: DateTime.now().subtract(Duration(days: 30)),
        );

        await _leaderboardService.updateUserData(currentUserData);
        _currentUser = currentUserData;

        // Reload leaderboard after updating current user
        final updatedLeaderboard = await _leaderboardService.getLeaderboard();
        setState(() {
          _leaderboardData = updatedLeaderboard;
          _isLoading = false;
        });
      } else {
        setState(() {
          _leaderboardData = leaderboardData;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading leaderboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DataController dataController = Get.find<DataController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Leaderboard'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLeaderboardData,
            tooltip: 'Refresh Leaderboard',
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: _showProfileDialog,
            tooltip: 'Edit Profile',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue.shade400, Colors.blue.shade900],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                // User's Current Position Card
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.amber.shade400, Colors.orange.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor.withValues(alpha: 0.26),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Obx(() => Text(
                              dataController.userName.value.isNotEmpty
                                ? dataController.userName.value
                                : 'Guest User',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            )),
                            const SizedBox(height: 4),
                            Obx(() => Text(
                              'Rank #${dataController.rank.value} • ${dataController.totalScore.value} pts',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            )),
                          ],
                        ),
                      ),
                      Obx(() => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Level ${dataController.getUserLevel()}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )),
                    ],
                  ),
                ),

                // Leaderboard List
                Expanded(
                  child: _isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : _leaderboardData.isEmpty
                          ? const Center(
                              child: Text(
                                'No leaderboard data available',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            )
                          : RefreshIndicator(
                              onRefresh: _loadLeaderboardData,
                              child: ListView.builder(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: _leaderboardData.length,
                                itemBuilder: (context, index) {
                                  final player = _leaderboardData[index];
                                  final isCurrentUser = _currentUser != null && player.id == _currentUser!.id;

                                  return AnimatedContainer(
                                    duration: Duration(milliseconds: 300 + (index * 100)),
                                    margin: const EdgeInsets.only(bottom: 12),
                                    child: _buildLeaderboardItem(
                                      rank: index + 1,
                                      name: player.name,
                                      score: player.totalScore,
                                      level: player.level,
                                      avatar: player.avatar,
                                      isCurrentUser: isCurrentUser,
                                      userData: player,
                                    ),
                                  );
                                },
                              ),
                            ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLeaderboardItem({
    required int rank,
    required String name,
    required int score,
    required int level,
    required String avatar,
    bool isCurrentUser = false,
    UserData? userData,
  }) {
    Color getRankColor() {
      switch (rank) {
        case 1:
          return Colors.amber;
        case 2:
          return Colors.grey.shade400;
        case 3:
          return Colors.brown;
        default:
          return Colors.blue;
      }
    }

    IconData getRankIcon() {
      switch (rank) {
        case 1:
          return Icons.emoji_events;
        case 2:
          return Icons.military_tech;
        case 3:
          return Icons.workspace_premium;
        default:
          return Icons.person;
      }
    }

    return Card(
      elevation: rank <= 3 ? 12 : (isCurrentUser ? 8 : 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: isCurrentUser
            ? BorderSide(color: Colors.amber, width: 2)
            : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: rank <= 3
              ? LinearGradient(
                  colors: [
                    getRankColor().withValues(alpha: 0.15),
                    getRankColor().withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : isCurrentUser
                  ? LinearGradient(
                      colors: [
                        Colors.amber.withValues(alpha: 0.1),
                        Colors.orange.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [
                        Colors.grey.withValues(alpha: 0.05),
                        Colors.grey.withValues(alpha: 0.02),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
          leading: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: getRankColor(),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: getRankColor().withValues(alpha: 0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (rank <= 3)
                  Icon(
                    getRankIcon(),
                    color: Colors.white,
                    size: 24,
                  )
                else
                  Text(
                    rank.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
              ],
            ),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  avatar,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: isCurrentUser
                                  ? Colors.amber.shade700
                                  : rank <= 3
                                      ? getRankColor()
                                      : Theme.of(context).colorScheme.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isCurrentUser)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'YOU',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (userData != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          ...userData!.getAchievementBadges().take(3).map(
                            (badge) => Padding(
                              padding: const EdgeInsets.only(right: 4),
                              child: Text(badge, style: const TextStyle(fontSize: 14)),
                            ),
                          ),
                          if (userData!.getAchievementBadges().length > 3)
                            Text(
                              '+${userData!.getAchievementBadges().length - 3}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.amber,
                ),
                const SizedBox(width: 4),
                Text(
                  'Level $level',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (userData != null) ...[
                  const SizedBox(width: 12),
                  Icon(
                    Icons.games,
                    size: 16,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${userData!.gamesPlayed} games',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: rank <= 3
                      ? getRankColor().withValues(alpha: 0.1)
                      : isCurrentUser
                          ? Colors.amber.withValues(alpha: 0.1)
                          : Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$score',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: rank <= 3
                        ? getRankColor()
                        : isCurrentUser
                            ? Colors.amber.shade700
                            : Colors.blue,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'points',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              if (userData != null && userData!.averageScore > 0) ...[
                const SizedBox(height: 2),
                Text(
                  'Avg: ${userData!.averageScore.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 10,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showProfileDialog() {
    final DataController dataController = Get.find<DataController>();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Edit Profile'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Choose your avatar:'),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: LeaderboardService.availableAvatars.map((avatar) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _updateUserAvatar(avatar);
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            avatar,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _updateUserAvatar(String avatar) async {
    final DataController dataController = Get.find<DataController>();
    await dataController.updateUserAvatar(avatar);
    await _loadLeaderboardData();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Avatar updated to $avatar'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }
}
